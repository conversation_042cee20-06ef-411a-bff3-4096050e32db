#!/usr/bin/env python3
"""
Test the new comprehensive staff profile functionality in admin dashboard
"""

import sys
from datetime import datetime, timed<PERSON>ta

def test_new_staff_profile_route():
    """Test if the new comprehensive staff profile route exists and works"""
    print("🧪 Testing New Comprehensive Staff Profile...")
    print("=" * 60)
    
    try:
        from app import app
        from database import get_db
        
        with app.app_context():
            db = get_db()
            
            # Test 1: Check if new route exists
            print("1️⃣ Testing new comprehensive staff profile route...")
            
            routes = [rule.rule for rule in app.url_map.iter_rules()]
            new_route = '/get_comprehensive_staff_profile'
            
            if new_route in routes:
                print(f"  ✓ Route '{new_route}' exists")
            else:
                print(f"  ✗ Route '{new_route}' missing")
                return False
            
            # Test 2: Check if old route is still there (for compatibility)
            print("2️⃣ Testing route compatibility...")
            
            old_route = '/get_staff_details'
            if old_route in routes:
                print(f"  ✓ Compatibility route '{old_route}' still exists")
            else:
                print(f"  ⚠️ Compatibility route '{old_route}' missing")
            
            # Test 3: Test database queries for comprehensive data
            print("3️⃣ Testing comprehensive data queries...")
            
            # Create test data if needed
            staff_count = db.execute('SELECT COUNT(*) as count FROM staff').fetchone()
            if staff_count['count'] == 0:
                print("  ℹ️ No staff data found, creating test staff...")
                
                # Create test school
                school_cursor = db.execute('''
                    INSERT INTO schools (name, address, contact_phone, contact_email) 
                    VALUES (?, ?, ?, ?)
                ''', ('Test School', 'Test Address', '1234567890', '<EMAIL>'))
                school_id = school_cursor.lastrowid
                
                # Create test staff
                staff_cursor = db.execute('''
                    INSERT INTO staff (staff_id, full_name, email, phone, department, position, school_id, password_hash)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', ('TEST001', 'Test Staff Member', '<EMAIL>', '9876543210', 
                      'IT', 'Developer', school_id, 'test_hash'))
                test_staff_id = staff_cursor.lastrowid
                
                # Create test attendance
                from datetime import date
                today = date.today()
                for i in range(10):
                    test_date = today - timedelta(days=i)
                    status = 'present' if i % 3 != 0 else ('late' if i % 5 == 0 else 'absent')
                    db.execute('''
                        INSERT INTO attendance (staff_id, school_id, date, time_in, time_out, status)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (test_staff_id, school_id, test_date, '09:00:00', '17:00:00', status))
                
                # Create test biometric verifications
                for i in range(5):
                    test_datetime = datetime.now() - timedelta(days=i)
                    verification_type = ['check-in', 'check-out', 'overtime-in', 'overtime-out'][i % 4]
                    db.execute('''
                        INSERT INTO biometric_verifications 
                        (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (test_staff_id, school_id, verification_type, test_datetime, '*************', 'fingerprint', 'success'))
                
                db.commit()
                print(f"  ✓ Created test data with staff ID: {test_staff_id}")
            else:
                print(f"  ✓ Found {staff_count['count']} existing staff members")
            
            # Test 4: Test comprehensive data retrieval
            print("4️⃣ Testing comprehensive data retrieval...")
            
            sample_staff = db.execute('SELECT id FROM staff LIMIT 1').fetchone()
            if sample_staff:
                staff_id = sample_staff['id']
                
                # Test staff info query
                staff_info = db.execute('''
                    SELECT s.*, sc.name as school_name 
                    FROM staff s 
                    LEFT JOIN schools sc ON s.school_id = sc.id 
                    WHERE s.id = ?
                ''', (staff_id,)).fetchone()
                
                if staff_info:
                    print(f"  ✓ Staff info query successful: {staff_info['full_name']}")
                else:
                    print("  ✗ Staff info query failed")
                    return False
                
                # Test attendance query
                thirty_days_ago = (datetime.now() - timedelta(days=30)).date()
                attendance = db.execute('''
                    SELECT date, time_in, time_out, overtime_in, overtime_out, status 
                    FROM attendance 
                    WHERE staff_id = ? AND date >= ?
                    ORDER BY date DESC
                ''', (staff_id, thirty_days_ago)).fetchall()
                
                print(f"  ✓ Attendance query successful: {len(attendance)} records")
                
                # Test biometric verifications query
                verifications = db.execute('''
                    SELECT verification_type, verification_time, verification_status, device_ip
                    FROM biometric_verifications 
                    WHERE staff_id = ? AND DATE(verification_time) >= ?
                    ORDER BY verification_time DESC
                    LIMIT 50
                ''', (staff_id, thirty_days_ago)).fetchall()
                
                print(f"  ✓ Biometric verifications query successful: {len(verifications)} records")
                
                # Test leave applications query
                leaves = db.execute('''
                    SELECT leave_type, start_date, end_date, reason, status, applied_at
                    FROM leave_applications
                    WHERE staff_id = ?
                    ORDER BY applied_at DESC
                    LIMIT 20
                ''', (staff_id,)).fetchall()
                
                print(f"  ✓ Leave applications query successful: {len(leaves)} records")
            
            return True
            
    except Exception as e:
        print(f"  ✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_structure():
    """Test if the admin dashboard template has the new modal structure"""
    print("\n📋 Testing New Template Structure...")
    print("=" * 60)
    
    try:
        with open('templates/admin_dashboard.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Test 1: Check for new modal
        print("1️⃣ Testing new staff profile modal...")
        if 'id="staffProfileModal"' in template_content:
            print("  ✓ New staff profile modal exists")
        else:
            print("  ✗ New staff profile modal missing")
            return False
        
        # Test 2: Check for modal content area
        print("2️⃣ Testing modal content area...")
        if 'id="staffProfileModalContent"' in template_content:
            print("  ✓ Modal content area exists")
        else:
            print("  ✗ Modal content area missing")
            return False
        
        # Test 3: Check for updated links
        print("3️⃣ Testing updated staff profile links...")
        if 'staff-profile-link' in template_content and 'staff-profile-btn' in template_content:
            print("  ✓ Updated staff profile links exist")
        else:
            print("  ✗ Updated staff profile links missing")
            return False
        
        # Test 4: Check for modal buttons
        print("4️⃣ Testing modal action buttons...")
        if 'id="editStaffProfileBtn"' in template_content and 'id="deleteStaffProfileBtn"' in template_content:
            print("  ✓ Modal action buttons exist")
        else:
            print("  ✗ Modal action buttons missing")
            return False
        
        # Test 5: Verify old card is removed
        print("5️⃣ Testing old staff profile card removal...")
        if 'id="staffProfileCard"' not in template_content:
            print("  ✓ Old staff profile card successfully removed")
        else:
            print("  ⚠️ Old staff profile card still exists")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Template test failed: {e}")
        return False

def test_javascript_structure():
    """Test if the JavaScript has the new comprehensive functions"""
    print("\n📄 Testing New JavaScript Structure...")
    print("=" * 60)
    
    try:
        with open('static/js/admin_dashboard.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Test 1: Check for new main function
        print("1️⃣ Testing new comprehensive function...")
        if 'function loadComprehensiveStaffProfile(' in js_content:
            print("  ✓ loadComprehensiveStaffProfile function exists")
        else:
            print("  ✗ loadComprehensiveStaffProfile function missing")
            return False
        
        # Test 2: Check for render function
        print("2️⃣ Testing render function...")
        if 'function renderComprehensiveStaffProfile(' in js_content:
            print("  ✓ renderComprehensiveStaffProfile function exists")
        else:
            print("  ✗ renderComprehensiveStaffProfile function missing")
            return False
        
        # Test 3: Check for button setup function
        print("3️⃣ Testing button setup function...")
        if 'function setupStaffProfileButtons(' in js_content:
            print("  ✓ setupStaffProfileButtons function exists")
        else:
            print("  ✗ setupStaffProfileButtons function missing")
            return False
        
        # Test 4: Check for helper functions
        print("4️⃣ Testing helper functions...")
        helper_functions = ['getStatusColor', 'getLeaveStatusColor', 'changeStaffPhoto']
        for func in helper_functions:
            if f'function {func}(' in js_content:
                print(f"  ✓ {func} function exists")
            else:
                print(f"  ✗ {func} function missing")
                return False
        
        # Test 5: Check for updated event handlers
        print("5️⃣ Testing updated event handlers...")
        if 'staff-profile-link' in js_content and 'staff-profile-btn' in js_content:
            print("  ✓ Updated event handlers exist")
        else:
            print("  ✗ Updated event handlers missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ✗ JavaScript test failed: {e}")
        return False

def main():
    """Run all tests for the new comprehensive staff profile"""
    print("🚀 Testing New Comprehensive Staff Profile Implementation...")
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    tests = [
        test_new_staff_profile_route,
        test_template_structure,
        test_javascript_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! New comprehensive staff profile is working perfectly!")
        print("\n📋 New Features Summary:")
        print("  ✅ Comprehensive staff profile modal")
        print("  ✅ Personal information display")
        print("  ✅ Attendance statistics with visual progress")
        print("  ✅ Tabbed interface for detailed data")
        print("  ✅ Attendance records table")
        print("  ✅ Biometric verifications history")
        print("  ✅ Leave applications tracking")
        print("  ✅ Edit and delete functionality")
        print("  ✅ Photo change capability")
        print("  ✅ Responsive design")
        print("\n🔄 How to use:")
        print("  1. Click on staff name or 'View' button in admin dashboard")
        print("  2. Comprehensive modal opens with all staff information")
        print("  3. Use tabs to navigate between different data sections")
        print("  4. Use 'Edit Profile' or 'Delete Staff' buttons as needed")
        return True
    else:
        print(f"⚠️ {total - passed} test(s) failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
