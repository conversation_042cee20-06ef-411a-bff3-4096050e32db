# ZK Biometric Device Integration Setup Guide

This guide will help you set up and configure ZK biometric devices with the VishnoRex Staff Attendance System.

## 📋 Prerequisites

### Hardware Requirements
- ZK Biometric Device (fingerprint/face recognition)
- Network connection (Ethernet recommended)
- Computer with Python 3.7+

### Software Requirements
- Python packages: `pyzk`, `pymysql` (already included in requirements.txt)
- MySQL Server (optional, for backup database)
- Network access to device IP: `*************`

## 🔧 Network Configuration

### Step 1: Configure Your Network
The ZK device is configured to use:
- **IP Address**: `*************`
- **Subnet Mask**: `*************`
- **Gateway**: `***********`
- **Port**: `4370`

#### Option A: Automatic Network Configuration
Run the network configuration script:
```bash
python network_config.py
```

#### Option B: Manual Network Configuration

**Windows:**
```cmd
# Run as Administrator
netsh interface ip set address name="Ethernet" static ************* ************* ***********
```

**Linux:**
```bash
# Replace eth0 with your interface name
sudo ip addr add *************/24 dev eth0
sudo ip route add default via ***********
```

### Step 2: Test Network Connectivity
```bash
ping *************
```

## 🔌 ZK Device Configuration

### Device Network Settings
1. Access device menu (usually Admin → Comm → Ethernet)
2. Set IP Address: `*************`
3. Set Subnet Mask: `*************`
4. Set Gateway: `***********`
5. Save and restart device

### Device Communication Settings
1. Go to Comm → TCP/IP
2. Enable TCP/IP communication
3. Set Port: `4370`
4. Save settings

## 💾 Database Setup

### SQLite (Main Database)
The system automatically uses the existing SQLite database (`vishnorex.db`).

### MySQL (Optional Backup Database)
1. **Install MySQL Server**
2. **Create Database:**
   ```sql
   CREATE DATABASE staff;
   CREATE USER 'attendance_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON staff.* TO 'attendance_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **Update Configuration** in `zk_biometric.py`:
   ```python
   mysql_config = {
       'host': 'localhost',
       'user': 'attendance_user',
       'password': 'secure_password',
       'database': 'staff'
   }
   ```

## 🚀 Installation & Testing

### Step 1: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Test ZK Integration
```bash
python test_zk_integration.py
```

### Step 3: Start the Application
```bash
python app.py
```

### Step 4: Access Web Interface
1. Open browser: `http://127.0.0.1:5000`
2. Login as admin
3. Click "Biometric Device" in the navigation
4. Test connection and sync attendance

## 📊 Using the Biometric Integration

### Web Interface Features

#### 1. Test Connection
- Click "Test Connection" to verify device connectivity
- Shows device status and number of registered users

#### 2. Sync Attendance
- Click "Sync Attendance" to pull attendance records
- Shows sync results for both SQLite and MySQL databases

#### 3. Load Users
- Click "Load Users from Device" to see registered users
- Helps verify user mapping between device and system

### Staff ID Mapping
The system maps ZK device User IDs to staff records:
- Device User ID → Staff ID in database
- Ensure staff records have matching IDs for proper sync

## 🔄 Attendance Sync Process

### How It Works
1. **Connect** to ZK device
2. **Disable** device temporarily (prevents interference)
3. **Retrieve** attendance records
4. **Map** device User IDs to staff records
5. **Insert/Update** attendance in SQLite database
6. **Backup** to MySQL database (if configured)
7. **Re-enable** device

### Punch Types
- **0**: Check In
- **1**: Check Out
- **2**: Break Out
- **3**: Break In

### Status Mapping
- Check In before 9:00 AM → `present`
- Check In after 9:00 AM → `late`
- Check Out → Updates time_out field

## 🛠️ Troubleshooting

### Connection Issues
```
❌ Failed to connect to device
```
**Solutions:**
- Check device power and network connection
- Verify IP address configuration
- Ensure device is not locked by another application
- Check firewall settings

### No Attendance Records
```
✅ Connection successful but 0 records
```
**Solutions:**
- Check if device has attendance data
- Verify date/time settings on device
- Clear old records if device memory is full

### User Mapping Issues
```
⚠️ Staff with ID XXX not found
```
**Solutions:**
- Ensure staff records exist in database
- Match device User IDs with staff IDs
- Use "Load Users" to see device user list

### MySQL Connection Failed
```
❌ MySQL connection failed
```
**Solutions:**
- Check MySQL server status
- Verify credentials in configuration
- Ensure database exists
- Check user permissions

## 📝 API Endpoints

### Sync Attendance
```
POST /sync_biometric_attendance
Parameters: device_ip (optional, default: *************)
```

### Test Connection
```
POST /test_biometric_connection
Parameters: device_ip (optional, default: *************)
```

### Get Device Users
```
GET /get_biometric_users
Parameters: device_ip (optional, default: *************)
```

## 🔒 Security Considerations

1. **Network Security**: Use secure network for device communication
2. **Database Access**: Use strong passwords for MySQL
3. **Device Access**: Restrict physical access to biometric device
4. **Regular Backups**: Backup attendance data regularly

## 📞 Support

For technical support:
1. Check device manual for specific model settings
2. Verify network configuration
3. Test with provided scripts
4. Check application logs for detailed error messages

## 🎯 Best Practices

1. **Regular Sync**: Set up automated sync schedule
2. **Data Backup**: Regular database backups
3. **Device Maintenance**: Keep device firmware updated
4. **User Management**: Regular user list synchronization
5. **Monitoring**: Monitor sync success rates
