# Enhanced Biometric Attendance System

## Overview

The enhanced biometric attendance system provides comprehensive time tracking with four distinct verification types:

- **Check-in**: Regular morning arrival
- **Check-out**: End of regular hours or lunch break
- **Overtime-in**: Starting overtime work
- **Overtime-out**: End of overtime work

Each verification requires biometric authentication and logs detailed timing information.

## Features

### 🔐 Biometric Verification Types

1. **Check-in**
   - Available anytime
   - Creates new attendance record for the day
   - Determines if arrival is late (after 9:00 AM)

2. **Check-out**
   - Only available after check-in
   - Updates regular work end time
   - Cannot be used without prior check-in

3. **Overtime-in**
   - Only available after check-in
   - Marks start of overtime work
   - Requires regular check-in first

4. **Overtime-out**
   - Only available after overtime-in
   - Marks end of overtime work
   - Cannot be used without prior overtime-in

### 📊 Database Schema

#### Enhanced Attendance Table
```sql
CREATE TABLE attendance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    staff_id INTEGER NOT NULL,
    school_id INTEGER NOT NULL,
    date DATE NOT NULL,
    time_in TIME,           -- Regular check-in time
    time_out TIME,          -- Regular check-out time
    overtime_in TIME,       -- Overtime start time
    overtime_out TIME,      -- Overtime end time
    status TEXT,
    notes TEXT,
    UNIQUE(staff_id, date)
);
```

#### Biometric Verification Log
```sql
CREATE TABLE biometric_verifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    staff_id INTEGER NOT NULL,
    school_id INTEGER NOT NULL,
    verification_type TEXT,     -- 'check-in', 'check-out', 'overtime-in', 'overtime-out'
    verification_time DATETIME, -- Exact timestamp of verification
    device_ip TEXT,            -- ZK device IP address
    biometric_method TEXT,     -- 'fingerprint', 'face', 'card', 'password'
    verification_status TEXT,  -- 'success', 'failed', 'retry'
    notes TEXT
);
```

### 🎯 Business Rules

1. **Sequential Verification**
   - Check-out requires check-in
   - Overtime-out requires overtime-in
   - Cannot skip steps in the workflow

2. **Daily Uniqueness**
   - Each verification type can only be used once per day
   - Prevents duplicate entries

3. **Time Validation**
   - Late arrival detection (after 9:00 AM)
   - Logical time sequence enforcement

### 🖥️ User Interface

#### Staff Dashboard Components

1. **Attendance Status Display**
   - Current day status
   - Regular hours (check-in/check-out times)
   - Overtime hours (overtime-in/overtime-out times)

2. **Verification Buttons**
   - Four action buttons with dynamic enabling/disabling
   - Visual feedback for available actions
   - Clear labeling with icons

3. **Biometric Scanner Interface**
   - Fingerprint scanner placeholder
   - Real-time verification feedback
   - Progress indicators

4. **Verification History**
   - Today's verification log
   - Timestamp, type, method, and status
   - Real-time updates

### 🔄 Workflow Process

#### 1. Staff Selects Verification Type
```javascript
// User clicks verification button
selectedVerificationType = 'check-in'; // or check-out, overtime-in, overtime-out
```

#### 2. Biometric Authentication
```javascript
// System performs biometric verification
fetch('/biometric_attendance', {
    method: 'POST',
    body: `verification_type=${verificationType}&biometric_method=fingerprint`
});
```

#### 3. Business Rule Validation
```python
# Server validates business rules
if verification_type == 'check-out':
    if not existing_attendance or not existing_attendance['time_in']:
        return error('Cannot check-out without checking in first')
```

#### 4. Biometric Verification
```python
# Verify against ZK device
verification_result = verify_staff_biometric(staff_id, device_ip, 'fingerprint')
if not verification_result['success']:
    return error('Biometric verification failed')
```

#### 5. Database Updates
```python
# Update attendance record
if verification_type == 'check-in':
    INSERT INTO attendance (staff_id, date, time_in, status)
elif verification_type == 'overtime-in':
    UPDATE attendance SET overtime_in = ? WHERE staff_id = ? AND date = ?

# Log verification
INSERT INTO biometric_verifications (staff_id, verification_type, verification_time, ...)
```

### 📱 API Endpoints

#### `/biometric_attendance` (POST)
Handles biometric verification and attendance logging.

**Parameters:**
- `verification_type`: check-in, check-out, overtime-in, overtime-out
- `biometric_method`: fingerprint, face, card, password
- `device_ip`: ZK device IP address

**Response:**
```json
{
    "success": true,
    "message": "Check-in recorded successfully",
    "verification_time": "2023-12-07 09:15:30",
    "verification_type": "check-in",
    "biometric_method": "fingerprint"
}
```

#### `/get_today_attendance_status` (GET)
Returns current day's attendance status and available actions.

**Response:**
```json
{
    "success": true,
    "attendance": {
        "time_in": "09:15:30",
        "time_out": null,
        "overtime_in": null,
        "overtime_out": null,
        "status": "present"
    },
    "verifications": [...],
    "available_actions": ["check-out", "overtime-in"]
}
```

#### `/get_biometric_verifications` (GET)
Returns verification history for the staff member.

### 🔧 Configuration

#### ZK Device Setup
```python
# Device configuration
DEVICE_IP = '*************'
DEVICE_PORT = 4370
BIOMETRIC_METHODS = ['fingerprint', 'face', 'card']
```

#### Business Rules Configuration
```python
# Time thresholds
LATE_ARRIVAL_TIME = datetime.time(9, 0)  # 9:00 AM
OVERTIME_START_TIME = datetime.time(17, 0)  # 5:00 PM
```

### 🚀 Usage Instructions

#### For Staff Members

1. **Login to Staff Dashboard**
   - Access the staff portal
   - Navigate to attendance section

2. **Select Verification Type**
   - Choose from available options (buttons are enabled/disabled based on current status)
   - Options: Check-in, Check-out, Overtime-in, Overtime-out

3. **Perform Biometric Verification**
   - Click "Start Authentication"
   - Place finger on biometric scanner
   - Wait for verification confirmation

4. **View Results**
   - Check verification status
   - View updated attendance times
   - Review verification history

#### For Administrators

1. **Monitor Staff Attendance**
   - View real-time attendance data
   - Access detailed verification logs
   - Generate attendance reports

2. **Manage Biometric Devices**
   - Configure device settings
   - Monitor device connectivity
   - Sync attendance data

### 🔍 Troubleshooting

#### Common Issues

1. **"Cannot check-out without checking in first"**
   - Ensure staff has completed check-in
   - Verify attendance record exists

2. **"Biometric verification failed"**
   - Check ZK device connectivity
   - Verify staff is enrolled on device
   - Ensure proper finger placement

3. **"Already checked in for today"**
   - Staff has already completed this verification type
   - Check verification history for confirmation

#### System Requirements

- ZK biometric device (IP: *************)
- Network connectivity to device
- Staff enrolled on biometric device
- Updated database schema

### 📈 Benefits

1. **Comprehensive Time Tracking**
   - Regular and overtime hours separately tracked
   - Detailed verification logging

2. **Enhanced Security**
   - Biometric authentication for all verifications
   - Audit trail for all activities

3. **Business Rule Enforcement**
   - Prevents logical errors in attendance
   - Ensures proper workflow sequence

4. **Real-time Feedback**
   - Immediate verification results
   - Live attendance status updates

5. **Detailed Reporting**
   - Complete verification history
   - Timestamp accuracy for payroll
