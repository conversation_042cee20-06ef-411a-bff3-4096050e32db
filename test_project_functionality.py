#!/usr/bin/env python3
"""
Comprehensive test script to verify the biometric attendance system functionality
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    try:
        import flask
        print("  ✓ Flask imported successfully")
        
        from app import app
        print("  ✓ Main app imported successfully")
        
        from database import get_db, init_db
        print("  ✓ Database module imported successfully")
        
        from zk_biometric import ZKBiometricDevice, process_device_attendance_automatically
        print("  ✓ ZK biometric module imported successfully")
        
        return True
    except Exception as e:
        print(f"  ✗ Import failed: {e}")
        return False

def test_database():
    """Test database initialization and structure"""
    print("\n🗄️ Testing database...")
    try:
        from app import app
        from database import init_db, get_db
        
        # Initialize database
        init_db(app)
        print("  ✓ Database initialized successfully")
        
        # Test database connection
        with app.app_context():
            db = get_db()
            
            # Check if required tables exist
            tables = ['staff', 'attendance', 'biometric_verifications', 'schools']
            for table in tables:
                result = db.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'").fetchone()
                if result:
                    print(f"  ✓ Table '{table}' exists")
                else:
                    print(f"  ✗ Table '{table}' missing")
                    return False
        
        return True
    except Exception as e:
        print(f"  ✗ Database test failed: {e}")
        return False

def test_routes():
    """Test if key routes are registered"""
    print("\n🛣️ Testing routes...")
    try:
        from app import app
        
        # Key routes to check
        key_routes = [
            '/',
            '/staff/dashboard',
            '/admin/dashboard',
            '/poll_device_attendance',
            '/get_latest_device_verifications',
            '/check_device_verification',
            '/biometric_attendance'
        ]
        
        registered_routes = [rule.rule for rule in app.url_map.iter_rules()]
        
        for route in key_routes:
            if route in registered_routes:
                print(f"  ✓ Route '{route}' registered")
            else:
                print(f"  ✗ Route '{route}' missing")
                return False
        
        print(f"  ✓ Total routes registered: {len(registered_routes)}")
        return True
    except Exception as e:
        print(f"  ✗ Route test failed: {e}")
        return False

def test_zk_biometric():
    """Test ZK biometric device functionality"""
    print("\n📱 Testing ZK biometric functionality...")
    try:
        from zk_biometric import ZKBiometricDevice
        
        # Test device initialization
        device = ZKBiometricDevice('*************')
        print("  ✓ ZK device object created successfully")
        
        # Test punch code mapping
        test_codes = [0, 1, 2, 3]
        expected_types = ['check-in', 'check-out', 'overtime-in', 'overtime-out']
        
        for code, expected in zip(test_codes, expected_types):
            result = device._map_punch_to_verification_type(code)
            if result == expected:
                print(f"  ✓ Punch code {code} maps to '{result}'")
            else:
                print(f"  ✗ Punch code {code} mapping failed: got '{result}', expected '{expected}'")
                return False
        
        return True
    except Exception as e:
        print(f"  ✗ ZK biometric test failed: {e}")
        return False

def test_templates():
    """Test if template files exist and are valid"""
    print("\n📄 Testing templates...")
    try:
        import os
        
        required_templates = [
            'templates/staff_dashboard.html',
            'templates/admin_dashboard.html',
            'templates/index.html'
        ]
        
        for template in required_templates:
            if os.path.exists(template):
                print(f"  ✓ Template '{template}' exists")
                
                # Check if template contains key elements
                with open(template, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if template == 'templates/staff_dashboard.html':
                    if 'biometric device' in content.lower():
                        print("  ✓ Staff dashboard contains biometric device instructions")
                    else:
                        print("  ⚠️ Staff dashboard missing biometric device instructions")
                        
                elif template == 'templates/admin_dashboard.html':
                    if 'attendanceTableBody' in content:
                        print("  ✓ Admin dashboard contains attendance table")
                    else:
                        print("  ⚠️ Admin dashboard missing attendance table")
            else:
                print(f"  ✗ Template '{template}' missing")
                return False
        
        return True
    except Exception as e:
        print(f"  ✗ Template test failed: {e}")
        return False

def test_static_files():
    """Test if static files exist"""
    print("\n📁 Testing static files...")
    try:
        import os
        
        required_js_files = [
            'static/js/staff_dashboard.js',
            'static/js/admin_dashboard.js'
        ]
        
        for js_file in required_js_files:
            if os.path.exists(js_file):
                print(f"  ✓ JavaScript file '{js_file}' exists")
                
                # Check for key functions
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if js_file == 'static/js/staff_dashboard.js':
                    if 'pollForDeviceVerifications' in content:
                        print("  ✓ Staff dashboard JS contains device polling")
                    else:
                        print("  ⚠️ Staff dashboard JS missing device polling")
                        
                elif js_file == 'static/js/admin_dashboard.js':
                    if 'pollDeviceForNewVerifications' in content:
                        print("  ✓ Admin dashboard JS contains device polling")
                    else:
                        print("  ⚠️ Admin dashboard JS missing device polling")
            else:
                print(f"  ✗ JavaScript file '{js_file}' missing")
                return False
        
        return True
    except Exception as e:
        print(f"  ✗ Static files test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting comprehensive project functionality test...")
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_database,
        test_routes,
        test_zk_biometric,
        test_templates,
        test_static_files
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ✗ Test failed with exception: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Project is working perfectly!")
        return True
    else:
        print(f"⚠️ {total - passed} test(s) failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
