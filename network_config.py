#!/usr/bin/env python3
"""
Network Configuration Script
Configure network settings for ZK biometric device communication
"""

import subprocess
import sys
import platform
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NetworkConfig:
    """Network configuration handler"""
    
    def __init__(self):
        self.os_type = platform.system().lower()
    
    def configure_windows_network(self, interface_name: str = "Ethernet", 
                                ip_address: str = "*************",
                                subnet_mask: str = "*************",
                                gateway: str = "***********"):
        """Configure network settings on Windows"""
        try:
            # Set static IP address
            cmd_ip = [
                "netsh", "interface", "ip", "set", "address",
                f"name={interface_name}",
                "static",
                ip_address,
                subnet_mask,
                gateway
            ]
            
            logger.info(f"Setting IP address to {ip_address}")
            result = subprocess.run(cmd_ip, capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                logger.info("IP address configured successfully")
                return True
            else:
                logger.error(f"Failed to set IP address: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error configuring Windows network: {str(e)}")
            return False
    
    def configure_linux_network(self, interface_name: str = "eth0",
                               ip_address: str = "*************",
                               subnet_mask: str = "*************", 
                               gateway: str = "***********"):
        """Configure network settings on Linux"""
        try:
            # Calculate CIDR notation
            cidr = self._subnet_to_cidr(subnet_mask)
            
            # Set IP address
            cmd_ip = ["sudo", "ip", "addr", "add", f"{ip_address}/{cidr}", "dev", interface_name]
            result = subprocess.run(cmd_ip, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to set IP address: {result.stderr}")
                return False
            
            # Set gateway
            cmd_gateway = ["sudo", "ip", "route", "add", "default", "via", gateway]
            result = subprocess.run(cmd_gateway, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.warning(f"Failed to set gateway (may already exist): {result.stderr}")
            
            logger.info(f"Network configured: {ip_address}/{cidr} via {gateway}")
            return True
            
        except Exception as e:
            logger.error(f"Error configuring Linux network: {str(e)}")
            return False
    
    def _subnet_to_cidr(self, subnet_mask: str) -> int:
        """Convert subnet mask to CIDR notation"""
        cidr_map = {
            "*************": 24,
            "***********": 16,
            "*********": 8,
            "***************": 25,
            "***************": 26,
            "***************": 27,
            "***************": 28,
            "***************": 29,
            "***************": 30
        }
        return cidr_map.get(subnet_mask, 24)
    
    def get_network_interfaces(self) -> list:
        """Get available network interfaces"""
        try:
            if self.os_type == "windows":
                result = subprocess.run(
                    ["netsh", "interface", "show", "interface"],
                    capture_output=True, text=True, shell=True
                )
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    interfaces = []
                    for line in lines[3:]:  # Skip header lines
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 4:
                                interfaces.append(parts[-1])
                    return interfaces
            else:
                result = subprocess.run(["ip", "link", "show"], capture_output=True, text=True)
                if result.returncode == 0:
                    interfaces = []
                    for line in result.stdout.split('\n'):
                        if ': ' in line and 'state' in line:
                            interface = line.split(':')[1].strip()
                            interfaces.append(interface)
                    return interfaces
        except Exception as e:
            logger.error(f"Error getting network interfaces: {str(e)}")
        
        return []
    
    def ping_device(self, ip_address: str = "*************") -> bool:
        """Test connectivity to ZK device"""
        try:
            if self.os_type == "windows":
                cmd = ["ping", "-n", "4", ip_address]
            else:
                cmd = ["ping", "-c", "4", ip_address]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Successfully pinged {ip_address}")
                return True
            else:
                logger.error(f"Failed to ping {ip_address}")
                return False
                
        except Exception as e:
            logger.error(f"Error pinging device: {str(e)}")
            return False
    
    def configure_network(self, interface_name: str = None,
                         ip_address: str = "*************",
                         subnet_mask: str = "*************",
                         gateway: str = "***********") -> bool:
        """Configure network based on OS"""
        
        # Get available interfaces if not specified
        if not interface_name:
            interfaces = self.get_network_interfaces()
            if not interfaces:
                logger.error("No network interfaces found")
                return False
            
            # Use first available interface
            interface_name = interfaces[0]
            logger.info(f"Using interface: {interface_name}")
        
        # Configure based on OS
        if self.os_type == "windows":
            return self.configure_windows_network(interface_name, ip_address, subnet_mask, gateway)
        else:
            return self.configure_linux_network(interface_name, ip_address, subnet_mask, gateway)

def main():
    """Main function to configure network for ZK device"""
    print("ZK Biometric Device Network Configuration")
    print("=" * 50)
    
    # Default settings for ZK device communication
    config = {
        'ip_address': '*************',
        'subnet_mask': '*************',
        'gateway': '***********'
    }
    
    # Get user input
    print(f"Current configuration:")
    print(f"IP Address: {config['ip_address']}")
    print(f"Subnet Mask: {config['subnet_mask']}")
    print(f"Gateway: {config['gateway']}")
    print()
    
    response = input("Do you want to use these settings? (y/n): ").lower()
    
    if response != 'y':
        config['ip_address'] = input("Enter IP address (*************): ") or config['ip_address']
        config['subnet_mask'] = input("Enter subnet mask (*************): ") or config['subnet_mask']
        config['gateway'] = input("Enter gateway (***********): ") or config['gateway']
    
    # Initialize network config
    net_config = NetworkConfig()
    
    # Show available interfaces
    interfaces = net_config.get_network_interfaces()
    if interfaces:
        print(f"\nAvailable network interfaces:")
        for i, interface in enumerate(interfaces):
            print(f"{i+1}. {interface}")
        
        try:
            choice = input(f"\nSelect interface (1-{len(interfaces)}) or press Enter for auto: ")
            if choice:
                interface_name = interfaces[int(choice) - 1]
            else:
                interface_name = None
        except (ValueError, IndexError):
            interface_name = None
    else:
        interface_name = None
    
    # Configure network
    print(f"\nConfiguring network...")
    success = net_config.configure_network(
        interface_name=interface_name,
        **config
    )
    
    if success:
        print("✅ Network configuration completed successfully!")
        
        # Test connectivity
        print(f"\nTesting connectivity to ZK device...")
        if net_config.ping_device(config['ip_address']):
            print("✅ ZK device is reachable!")
        else:
            print("❌ Cannot reach ZK device. Please check:")
            print("   - Device is powered on")
            print("   - Network cable is connected")
            print("   - Device IP matches configuration")
    else:
        print("❌ Network configuration failed!")
        print("Please run as administrator/sudo and try again.")

if __name__ == '__main__':
    main()
